<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>GPS追踪系统 - OpenStreetMap导航版</title>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <script>
        console.log('🗺️ 使用OpenStreetMap免费地图服务');
        console.log('�️ 集成OSRM免费路径规划');
        console.log('🌐 当前域名:', window.location.hostname);
        console.log('🌐 当前协议:', window.location.protocol);
    </script>
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: #1890ff;
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .info-panel {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item {
            text-align: center;
            flex: 1;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        #map {
            width: calc(100% - 20px);
            height: 500px;
            margin: 10px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #40a9ff;
        }
        
        .online {
            color: #52c41a;
        }
        
        .offline {
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>�️ GPS追踪系统 - OpenStreetMap导航版</h1>
        <p>免费路径规划 | 无需API密钥 | 符合交通规则</p>
    </div>

    <div class="info-panel">
        <div class="status">
            <div class="status-item">
                <div class="status-value" id="latitude">26.885054837223990</div>
                <div class="status-label">纬度 (°N)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="longitude">112.679572502899990</div>
                <div class="status-label">经度 (°E)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="altitude">68.0</div>
                <div class="status-label">海拔 (m)</div>
            </div>
            <div class="status-item">
                <div class="status-value online" id="status">在线</div>
                <div class="status-label">状态</div>
            </div>
        </div>
        <div style="text-align: center; margin-top: 10px;">
            <span>📍 当前位置：衡阳市体育中心</span>
            <span style="margin-left: 20px;">⏰ 更新时间：<span id="updateTime">--</span></span>
        </div>
    </div>
    
    <div id="map"></div>
    
    <div class="controls">
        <button class="btn" onclick="centerMap()">📍 回到中心</button>
        <button class="btn" onclick="toggleTracking()">📊 轨迹追踪</button>
        <button class="btn" onclick="simulateSerialInput()">🎯 测试WANDA导航</button>
        <button class="btn" onclick="testCustomRoute()">🧠 自定义路径规划</button>
        <button class="btn" onclick="planCustomRoute()">📝 输入坐标规划</button>
        <button class="btn" onclick="clearNavigationRoute()">🧹 清除路径</button>
        <button class="btn" onclick="navigateToGaotie()">🚄 导航到高铁站</button>
        <button class="btn" onclick="navigateToHospital()">🏥 导航到医院</button>
        <button class="btn" onclick="navigateToStation()">🚂 导航到火车站</button>
        <button class="btn" onclick="testThingSpeakConnection()">🔧 测试ThingSpeak</button>
        <button class="btn" onclick="openThingSpeak()">🌐 ThingSpeak数据</button>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <script type="text/javascript">
        // OpenStreetMap + Leaflet 变量
        var map, marker, trackingPath = [];
        var isTracking = false;

        // 导航相关变量
        var navigationRoute = null;
        var startMarker = null;
        var endMarker = null;
        var isNavigating = false;
        var routePolyline = null; // 存储路径线条

        // ThingSpeak配置 - 使用您提供的正确API密钥
        const THINGSPEAK_CHANNEL = '3014831';
        const THINGSPEAK_READ_KEY = 'V64RR7CZJ9Z4O7ED';  // 使用正确的读取API密钥
        const THINGSPEAK_WRITE_KEY = 'LU22ZUP4ZTFK4IY9'; // 写入API密钥

        // 衡阳师范学院默认坐标
        const DEFAULT_LAT = 26.8812;
        const DEFAULT_LON = 112.6769;

        // 预设目的地（整合自OpenStreetMap导航版本）
        const DESTINATIONS = {
            'wanda': { lat: 26.8869, lon: 112.6758, name: '酃湖万达广场' },
            'gaotie': { lat: 26.8945, lon: 112.6123, name: '衡阳东高铁站' },
            'yiyuan': { lat: 26.8756, lon: 112.6234, name: '南华大学附属第一医院' },
            'huochezhan': { lat: 26.8834, lon: 112.6167, name: '衡阳火车站' }
        };

        // GPS数据滤波器（整合自OpenStreetMap导航版本）
        class GPSFilter {
            constructor() {
                this.history = [];
                this.maxHistory = 5;
                this.maxJumpDistance = 0.01; // 约1km
                this.lastValidPosition = null;
            }

            filter(lat, lon) {
                // 基本范围检查
                if (lat < -90 || lat > 90 || lon < -180 || lon > 180) {
                    console.log(`⚠️ GPS坐标超出有效范围: ${lat}, ${lon}`);
                    return this.lastValidPosition;
                }

                // 初始化
                if (!this.lastValidPosition) {
                    this.lastValidPosition = { lat, lon };
                    this.history.push({ lat, lon });
                    console.log(`📍 GPS滤波器初始化: ${lat.toFixed(6)}, ${lon.toFixed(6)}`);
                    return { lat, lon };
                }

                // 计算距离
                const latDiff = lat - this.lastValidPosition.lat;
                const lonDiff = lon - this.lastValidPosition.lon;
                const distance = Math.sqrt(latDiff * latDiff + lonDiff * lonDiff);

                // 检查异常跳跃
                if (distance > this.maxJumpDistance) {
                    console.log(`⚠️ GPS异常跳跃被过滤: 距离 ${distance.toFixed(6)}`);
                    return this.lastValidPosition; // 返回上次有效位置
                }

                // 添加到历史记录
                this.history.push({ lat, lon });
                if (this.history.length > this.maxHistory) {
                    this.history.shift();
                }

                // 计算移动平均
                const avgLat = this.history.reduce((sum, pos) => sum + pos.lat, 0) / this.history.length;
                const avgLon = this.history.reduce((sum, pos) => sum + pos.lon, 0) / this.history.length;

                this.lastValidPosition = { lat: avgLat, lon: avgLon };
                return this.lastValidPosition;
            }
        }

        const gpsFilter = new GPSFilter();

        // 自定义路径规划系统
        var CustomRouteEngine = {
            // 衡阳市主要道路网络数据 (简化版)
            roadNetwork: [
                // 主要道路节点 [经度, 纬度, 道路名称, 道路类型]
                {lng: 112.6769, lat: 26.8812, name: "衡阳师范学院", type: "landmark"},
                {lng: 112.6796, lat: 26.8851, name: "体育中心", type: "landmark"},
                {lng: 112.675797, lat: 26.886900, name: "酃湖万达广场", type: "landmark"},
                {lng: 112.6720, lat: 26.8890, name: "火车站", type: "landmark"},
                {lng: 112.6800, lat: 26.8900, name: "市政府", type: "landmark"},
                {lng: 112.6750, lat: 26.8800, name: "中心医院", type: "landmark"},
                {lng: 112.6850, lat: 26.8850, name: "商业中心", type: "landmark"},
                {lng: 112.6780, lat: 26.8830, name: "解放路", type: "main_road"},
                {lng: 112.6740, lat: 26.8840, name: "蒸阳路", type: "main_road"},
                {lng: 112.6810, lat: 26.8870, name: "华新路", type: "main_road"}
            ],

            // 计算两点间距离 (米)
            calculateDistance: function(lat1, lng1, lat2, lng2) {
                var R = 6371000; // 地球半径(米)
                var dLat = (lat2 - lat1) * Math.PI / 180;
                var dLng = (lng2 - lng1) * Math.PI / 180;
                var a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                        Math.sin(dLng/2) * Math.sin(dLng/2);
                var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                return R * c;
            },

            // 智能路径规划算法
            planRoute: function(startLng, startLat, endLng, endLat) {
                console.log('🧠 开始自定义路径规划...');
                console.log('起点:', startLng, startLat);
                console.log('终点:', endLng, endLat);

                var route = {
                    waypoints: [],
                    distance: 0,
                    estimatedTime: 0,
                    instructions: []
                };

                // 1. 添加起点
                route.waypoints.push([startLng, startLat]);
                route.instructions.push("从起点出发");

                // 2. 寻找中间路径点 (基于道路网络)
                var intermediatePoints = this.findIntermediatePoints(startLng, startLat, endLng, endLat);

                // 3. 添加中间点
                for (var i = 0; i < intermediatePoints.length; i++) {
                    route.waypoints.push([intermediatePoints[i].lng, intermediatePoints[i].lat]);
                    route.instructions.push("经过 " + intermediatePoints[i].name);
                }

                // 4. 添加终点
                route.waypoints.push([endLng, endLat]);
                route.instructions.push("到达目的地");

                // 5. 计算总距离和时间
                route.distance = this.calculateRouteDistance(route.waypoints);
                route.estimatedTime = this.estimateTime(route.distance);

                console.log('✅ 路径规划完成:', route);
                return route;
            },

            // 寻找中间路径点
            findIntermediatePoints: function(startLng, startLat, endLng, endLat) {
                var intermediatePoints = [];
                var maxDetourDistance = 2000; // 最大绕行距离(米)

                // 遍历道路网络，寻找合适的中间点
                for (var i = 0; i < this.roadNetwork.length; i++) {
                    var point = this.roadNetwork[i];

                    // 计算该点到起点和终点的距离
                    var distToStart = this.calculateDistance(startLat, startLng, point.lat, point.lng);
                    var distToEnd = this.calculateDistance(point.lat, point.lng, endLat, endLng);
                    var directDist = this.calculateDistance(startLat, startLng, endLat, endLng);

                    // 如果通过该点的总距离不超过直线距离的1.5倍，则考虑加入路径
                    if (distToStart + distToEnd < directDist * 1.5 &&
                        distToStart > 500 && distToEnd > 500) { // 避免太近的点
                        intermediatePoints.push({
                            lng: point.lng,
                            lat: point.lat,
                            name: point.name,
                            priority: distToStart + distToEnd
                        });
                    }
                }

                // 按优先级排序，选择最优路径点
                intermediatePoints.sort(function(a, b) {
                    return a.priority - b.priority;
                });

                // 最多选择2个中间点
                return intermediatePoints.slice(0, 2);
            },

            // 计算路径总距离
            calculateRouteDistance: function(waypoints) {
                var totalDistance = 0;
                for (var i = 0; i < waypoints.length - 1; i++) {
                    var dist = this.calculateDistance(
                        waypoints[i][1], waypoints[i][0],
                        waypoints[i+1][1], waypoints[i+1][0]
                    );
                    totalDistance += dist;
                }
                return totalDistance;
            },

            // 估算行驶时间 (考虑交通规则)
            estimateTime: function(distance) {
                // 城市道路平均速度: 30km/h
                // 主干道平均速度: 50km/h
                // 考虑红绿灯等待时间
                var avgSpeed = 35; // km/h
                var timeInHours = (distance / 1000) / avgSpeed;
                var timeInMinutes = timeInHours * 60;

                // 添加红绿灯等待时间 (每公里约2分钟)
                var trafficDelay = (distance / 1000) * 2;

                return Math.round(timeInMinutes + trafficDelay);
            },

            // 生成导航指令
            generateInstructions: function(waypoints) {
                var instructions = [];

                for (var i = 0; i < waypoints.length - 1; i++) {
                    var current = waypoints[i];
                    var next = waypoints[i + 1];

                    // 计算方向
                    var bearing = this.calculateBearing(current[1], current[0], next[1], next[0]);
                    var direction = this.getDirection(bearing);
                    var distance = this.calculateDistance(current[1], current[0], next[1], next[0]);

                    if (i === 0) {
                        instructions.push("出发，向" + direction + "行驶 " + Math.round(distance) + "米");
                    } else if (i === waypoints.length - 2) {
                        instructions.push("继续向" + direction + "行驶 " + Math.round(distance) + "米，到达目的地");
                    } else {
                        instructions.push("向" + direction + "行驶 " + Math.round(distance) + "米");
                    }
                }

                return instructions;
            },

            // 计算方位角
            calculateBearing: function(lat1, lng1, lat2, lng2) {
                var dLng = (lng2 - lng1) * Math.PI / 180;
                var lat1Rad = lat1 * Math.PI / 180;
                var lat2Rad = lat2 * Math.PI / 180;

                var y = Math.sin(dLng) * Math.cos(lat2Rad);
                var x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
                        Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);

                var bearing = Math.atan2(y, x) * 180 / Math.PI;
                return (bearing + 360) % 360;
            },

            // 根据方位角获取方向
            getDirection: function(bearing) {
                var directions = ["北", "东北", "东", "东南", "南", "西南", "西", "西北"];
                var index = Math.round(bearing / 45) % 8;
                return directions[index];
            }
        };
        
        // 初始化OpenStreetMap地图
        function initMap() {
            console.log('🗺️ 初始化OpenStreetMap...');

            // 创建Leaflet地图
            map = L.map('map').setView([DEFAULT_LAT, DEFAULT_LON], 16);

            // 添加OpenStreetMap图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19
            }).addTo(map);

            console.log('✅ OpenStreetMap地图初始化完成');

            // 初始化OSRM路径规划服务（免费）
            console.log('🛣️ OSRM路径规划服务已就绪（免费，无需API密钥）');

            // 创建当前位置标记
            marker = L.marker([DEFAULT_LAT, DEFAULT_LON])
                .addTo(map);

            // 添加信息弹窗（使用Leaflet API）
            marker.bindPopup('<div style="padding:10px;"><h4>🏫 衡阳师范学院</h4><p>📍 GPS实时定位点</p><p>🕐 最后更新：' + new Date().toLocaleString() + '</p></div>');
        }
        
        // 刷新数据（仅用于手动刷新显示）
        function refreshData() {
            // 不再模拟GPS数据，只刷新当前显示
            centerMap();
        }

        // OSRM路径规划函数
        async function planRouteWithOSRM(startLat, startLon, endLat, endLon) {
            try {
                console.log('🚗 使用OSRM进行路径规划...');

                const url = `https://router.project-osrm.org/route/v1/driving/${startLon},${startLat};${endLon},${endLat}?overview=full&geometries=geojson&steps=true`;

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                    const route = data.routes[0];
                    console.log(`✅ OSRM路径规划成功: ${(route.distance/1000).toFixed(1)}km, ${Math.round(route.duration/60)}分钟`);

                    return {
                        coordinates: route.geometry.coordinates,
                        distance: route.distance,
                        duration: route.duration,
                        steps: route.legs[0].steps,
                        provider: 'OSRM'
                    };
                } else {
                    throw new Error('OSRM返回无效路径');
                }
            } catch (error) {
                console.error(`❌ OSRM路径规划失败: ${error.message}`);
                return null;
            }
        }

        // 在地图上显示OSRM路径
        function displayOSRMRoute(routeData) {
            // 清除之前的路径
            if (routePolyline) {
                map.removeLayer(routePolyline);
            }
            if (startMarker) {
                map.removeLayer(startMarker);
            }
            if (endMarker) {
                map.removeLayer(endMarker);
            }

            if (!routeData || !routeData.coordinates) {
                console.error('❌ 无效的路径数据');
                return false;
            }

            // 转换坐标格式（经度,纬度 → 纬度,经度）
            const latLngs = routeData.coordinates.map(coord => [coord[1], coord[0]]);

            // 创建路径线
            routePolyline = L.polyline(latLngs, {
                color: '#2E8B57',
                weight: 5,
                opacity: 0.8
            }).addTo(map);

            // 添加起点标记
            const startPoint = latLngs[0];
            startMarker = L.marker(startPoint, {
                icon: L.divIcon({
                    className: 'route-marker start-marker',
                    html: '🚗',
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                })
            }).addTo(map).bindPopup('🚗 起点');

            // 添加终点标记
            const endPoint = latLngs[latLngs.length - 1];
            endMarker = L.marker(endPoint, {
                icon: L.divIcon({
                    className: 'route-marker end-marker',
                    html: '🏁',
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                })
            }).addTo(map).bindPopup('🏁 终点');

            // 调整地图视野以显示完整路径
            map.fitBounds(routePolyline.getBounds(), { padding: [20, 20] });

            console.log(`✅ 路径显示完成，使用${routeData.provider}服务`);
            return true;
        }





        // 解析GPS数据 - 专用于高德地图GPS追踪系统
        function parseGPSData(data) {
            try {
                console.log('高德地图接收GPS数据:', data);

                // 格式1: "AMAP_GPS:26.885054,112.679572,68.0" (高德地图专用格式)
                if (data.includes('AMAP_GPS:')) {
                    const coords = data.replace('AMAP_GPS:', '').trim();
                    const parts = coords.split(',');
                    if (parts.length >= 2) {
                        const lat = parseFloat(parts[0]);
                        const lon = parseFloat(parts[1]);
                        const alt = parts.length > 2 ? parseFloat(parts[2]) : 68.0;

                        if (!isNaN(lat) && !isNaN(lon)) {
                            updateRealTimeGPS(lat, lon, alt);
                            console.log('高德地图位置更新成功:', lat, lon, alt);
                            return;
                        }
                    }
                }

                // 格式2: "GPS_MAP:26.885054,112.679572" (兼容格式)
                if (data.includes('GPS_MAP:')) {
                    const coords = data.replace('GPS_MAP:', '').trim();
                    const parts = coords.split(',');
                    if (parts.length >= 2) {
                        const lat = parseFloat(parts[0]);
                        const lon = parseFloat(parts[1]);
                        const alt = parts.length > 2 ? parseFloat(parts[2]) : 68.0;

                        if (!isNaN(lat) && !isNaN(lon)) {
                            updateRealTimeGPS(lat, lon, alt);
                            return;
                        }
                    }
                }

                // 格式2: "LAT:26.885054,LON:112.679572,ALT:68.5"
                if (data.includes('LAT:') && data.includes('LON:')) {
                    const latMatch = data.match(/LAT:([\d.-]+)/);
                    const lonMatch = data.match(/LON:([\d.-]+)/);
                    const altMatch = data.match(/ALT:([\d.-]+)/);

                    if (latMatch && lonMatch) {
                        const lat = parseFloat(latMatch[1]);
                        const lon = parseFloat(lonMatch[1]);
                        const alt = altMatch ? parseFloat(altMatch[1]) : 68.0;

                        updateRealTimeGPS(lat, lon, alt);
                        return;
                    }
                }

                // 格式3: NMEA格式解析
                if (data.startsWith('$GPGGA') || data.startsWith('$GPRMC') || data.startsWith('$GNRMC')) {
                    parseNMEAData(data);
                    return;
                }

                // 格式4: 简单的经纬度格式 "26.885054,112.679572"
                const coords = data.trim().split(',');
                if (coords.length >= 2) {
                    const lat = parseFloat(coords[0]);
                    const lon = parseFloat(coords[1]);
                    const alt = coords.length > 2 ? parseFloat(coords[2]) : 68.0;

                    if (!isNaN(lat) && !isNaN(lon) && lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180) {
                        updateRealTimeGPS(lat, lon, alt);
                        return;
                    }
                }

                console.log('未识别的GPS数据格式:', data);

            } catch (error) {
                console.error('GPS数据解析失败:', error);
            }
        }

        // 解析NMEA格式数据
        function parseNMEAData(nmea) {
            try {
                const parts = nmea.split(',');

                if (nmea.startsWith('$GPGGA') && parts.length >= 6) {
                    // $GPGGA格式解析
                    const lat = parseNMEACoordinate(parts[2], parts[3]);
                    const lon = parseNMEACoordinate(parts[4], parts[5]);
                    const alt = parseFloat(parts[9]) || 68.0;

                    if (lat && lon) {
                        updateRealTimeGPS(lat, lon, alt);
                    }
                }
            } catch (error) {
                console.error('NMEA数据解析失败:', error);
            }
        }

        // 解析NMEA坐标格式
        function parseNMEACoordinate(coord, direction) {
            if (!coord || !direction) return null;

            const degrees = Math.floor(parseFloat(coord) / 100);
            const minutes = parseFloat(coord) % 100;
            let decimal = degrees + minutes / 60;

            if (direction === 'S' || direction === 'W') {
                decimal = -decimal;
            }

            return decimal;
        }

        // 更新实时GPS数据
        function updateRealTimeGPS(lat, lon, alt) {
            // 更新显示
            updateGPSDisplay(lat, lon, alt);
            updateMapPosition(lon, lat);

            // 上传数据 - 已禁用，只从ESP01读取数据，不上传
            // uploadGPSData(lat, lon, alt);

            // 更新时间戳 - 只在接收到真实GPS数据时更新
            var now = new Date();
            var formattedDate = now.getFullYear() + "/" +
                               padZero(now.getMonth() + 1) + "/" +
                               padZero(now.getDate()) + " " +
                               padZero(now.getHours()) + ":" +
                               padZero(now.getMinutes()) + ":" +
                               padZero(now.getSeconds());

            document.getElementById('updateTime').textContent = formattedDate;
            document.getElementById('status').textContent = '实时GPS';
            document.getElementById('status').className = 'status-value online';

            // 保存最后更新时间到localStorage
            localStorage.setItem('lastGpsUpdateTime', formattedDate);
        }

        // 辅助函数：补零
        function padZero(num) {
            return num < 10 ? '0' + num : num;
        }

        // 从ThingSpeak获取ESP01上传的GPS数据 (使用fetch方式)
        function loadGPSFromThingSpeak() {
            console.log('🔄 正在从ThingSpeak获取GPS数据...');
            console.log('🔑 使用读取API密钥:', THINGSPEAK_READ_KEY);

            // 使用正确的API密钥和多种请求方式
            var thingSpeakURL = `https://api.thingspeak.com/channels/${THINGSPEAK_CHANNEL}/feeds/last.json?api_key=${THINGSPEAK_READ_KEY}`;

            // 备用URL（无API密钥，适用于公开频道）
            var backupURL = `https://api.thingspeak.com/channels/${THINGSPEAK_CHANNEL}/feeds/last.json`;

            // 首先尝试使用API密钥
            tryFetchThingSpeak(thingSpeakURL)
                .then(data => {
                    if (data) {
                        processThingSpeakData(data);
                    } else {
                        // 如果失败，尝试备用URL
                        console.log('🔄 尝试备用URL...');
                        return tryFetchThingSpeak(backupURL);
                    }
                })
                .then(data => {
                    if (data) {
                        processThingSpeakData(data);
                    } else {
                        // 如果都失败，使用默认位置
                        console.log('⚠️ 所有ThingSpeak请求都失败，使用默认位置');
                        useDefaultLocation();
                    }
                })
                .catch(error => {
                    console.log('❌ ThingSpeak请求失败:', error.message);
                    useDefaultLocation();
                });
        }

        // 尝试获取ThingSpeak数据
        async function tryFetchThingSpeak(url) {
            try {
                console.log('📡 请求URL:', url);
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    mode: 'cors'
                });

                console.log('ThingSpeak响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('✅ ThingSpeak原始数据:', data);

                // 检查是否返回-1（表示没有数据或频道私有）
                if (data === -1) {
                    console.log('❌ ThingSpeak返回-1，可能是频道私有或没有数据');
                    return null;
                }

                return data;

            } catch (error) {
                console.log('❌ 请求失败:', error.message);
                return null;
            }
        }

        // 处理ThingSpeak数据（已整合GPS滤波功能）
        function processThingSpeakData(data) {
            if (data && data.field1 && data.field2) {
                var rawLat = parseFloat(data.field1);
                var rawLon = parseFloat(data.field2);

                // 解析Field3：格式为 "高度|导航指令" 或 "高度"
                var alt = 68.0;
                var navigationCmd = null;

                if (data.field3) {
                    var field3Parts = data.field3.toString().split('|');
                    alt = parseFloat(field3Parts[0]) || 68.0;
                    if (field3Parts.length > 1 && field3Parts[1] !== '') {
                        navigationCmd = field3Parts[1];
                    }
                }

                console.log('📍 从ThingSpeak读取原始GPS数据:', rawLat, rawLon, alt);
                console.log('⏰ 数据时间:', data.created_at);
                if (navigationCmd) {
                    console.log('🗺️ 检测到导航指令:', navigationCmd);
                }

                // 使用GPS滤波器处理数据
                const filteredPos = gpsFilter.filter(rawLat, rawLon);
                if (filteredPos) {
                    // 使用滤波后的数据更新地图
                    updateRealTimeGPS(filteredPos.lat, filteredPos.lon, alt);

                    // 如果原始数据和滤波数据差异较大，显示警告
                    const latDiff = Math.abs(rawLat - filteredPos.lat);
                    const lonDiff = Math.abs(rawLon - filteredPos.lon);
                    if (latDiff > 0.001 || lonDiff > 0.001) {
                        console.log(`🔧 GPS数据已平滑处理: 原始(${rawLat.toFixed(6)}, ${rawLon.toFixed(6)}) -> 滤波(${filteredPos.lat.toFixed(6)}, ${filteredPos.lon.toFixed(6)})`);
                    }
                } else {
                    console.log('⚠️ GPS数据无效，使用默认位置');
                    updateRealTimeGPS(DEFAULT_LAT, DEFAULT_LON, alt);
                }

                // 处理导航指令
                if (navigationCmd) {
                    processNavigationData(navigationCmd);
                }

                // 更新状态为在线
                document.getElementById('status').textContent = 'ESP01在线';
                document.getElementById('status').className = 'status-value online';

                // 更新时间显示
                var updateTime = new Date(data.created_at).toLocaleString('zh-CN');
                document.getElementById('updateTime').textContent = updateTime;
                localStorage.setItem('lastGpsUpdateTime', updateTime);

                console.log('✅ GPS数据更新成功！');
            } else {
                console.log('❌ ThingSpeak数据格式不正确:', data);
                document.getElementById('status').textContent = '数据格式错误';
                document.getElementById('status').className = 'status-value offline';

                // 使用默认位置
                useDefaultLocation();
            }
        }

        // 使用默认位置
        function useDefaultLocation() {
            console.log('🏫 使用默认位置：衡阳师范学院');
            updateRealTimeGPS(26.8812, 112.6769, 68.0);
            document.getElementById('status').textContent = '使用默认位置';
            document.getElementById('status').className = 'status-value online';

            // 使用本地模拟GPS数据（模拟ESP01的行为）
            simulateLocalGPS();
        }

        // 本地GPS模拟函数（模拟ESP01的行为）
        function simulateLocalGPS() {
            // 模拟ESP01生成的GPS数据（衡阳师范学院附近）
            var baseLat = 26.8812;
            var baseLon = 112.6769;
            var baseAlt = 68.0;

            // 添加小幅度随机变化（模拟真实GPS的微小漂移）
            var seed = Date.now() % 1000;
            var lat = baseLat + (seed % 100) * 0.0001;
            var lon = baseLon + (seed % 100) * 0.0001;
            var alt = baseAlt + (seed % 50);

            console.log('本地模拟GPS数据:', lat, lon, alt);

            // 更新地图位置
            updateRealTimeGPS(lat, lon, alt);

            // 更新状态
            document.getElementById('status').textContent = '本地模拟';
            document.getElementById('status').className = 'status-value online';

            // 更新时间显示
            var updateTime = new Date().toLocaleString('zh-CN');
            document.getElementById('updateTime').textContent = updateTime;
            localStorage.setItem('lastGpsUpdateTime', updateTime);
        }

        // 实时上传GPS数据到ThingSpeak和高德地图
        function uploadGPSData(lat, lon, alt) {
            // 上传到ThingSpeak (使用正确的写入API Key)
            var thingSpeakURL = `https://api.thingspeak.com/update?api_key=${THINGSPEAK_WRITE_KEY}`;
            thingSpeakURL += '&field1=' + lat.toFixed(6);
            thingSpeakURL += '&field2=' + lon.toFixed(6);

            // Field3格式：高度|导航指令（如果有的话）
            var field3Value = alt.toFixed(1);
            if (window.pendingNavigationCommand) {
                field3Value += '|' + window.pendingNavigationCommand;
                window.pendingNavigationCommand = null; // 清除待发送的指令
            }
            thingSpeakURL += '&field3=' + encodeURIComponent(field3Value);

            console.log('📤 上传GPS数据到ThingSpeak:', {
                latitude: lat.toFixed(6),
                longitude: lon.toFixed(6),
                altitude: alt.toFixed(1),
                url: thingSpeakURL
            });

            // 使用JSONP方式上传数据
            var script = document.createElement('script');
            script.src = thingSpeakURL + '&callback=uploadCallback';
            document.head.appendChild(script);
            document.head.removeChild(script);

            // 同时在控制台显示上传信息
            console.log('高德地图GPS数据已上传:', {
                latitude: lat.toFixed(6),
                longitude: lon.toFixed(6),
                altitude: alt.toFixed(1),
                timestamp: new Date().toLocaleString(),
                location: '衡阳市体育中心',
                mapAPI: '高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1',
                thingSpeakAPI: 'LU22ZUP4ZTFK4IY9'
            });
        }

        // 上传回调函数
        function uploadCallback(response) {
            if (response > 0) {
                console.log('ThingSpeak上传成功，Entry ID:', response);
            } else {
                console.log('ThingSpeak上传失败');
            }
        }

        // 更新GPS显示
        function updateGPSDisplay(lat, lon, alt) {
            document.getElementById('latitude').textContent = lat.toFixed(6);
            document.getElementById('longitude').textContent = lon.toFixed(6);
            document.getElementById('altitude').textContent = alt.toFixed(1);
        }
        
        // 更新地图位置（使用Leaflet API）
        function updateMapPosition(lon, lat) {
            var newPos = [lat, lon]; // Leaflet使用[纬度, 经度]格式
            marker.setLatLng(newPos);

            // 更新弹窗内容
            marker.bindPopup(`<div style="padding:10px;">
                <h4>📍 当前位置</h4>
                <p>纬度: ${lat.toFixed(6)}°</p>
                <p>经度: ${lon.toFixed(6)}°</p>
                <p>🕐 更新时间: ${new Date().toLocaleString()}</p>
            </div>`);

            if (isTracking) {
                trackingPath.push(newPos);
                if (trackingPath.length > 1) {
                    // 使用Leaflet绘制轨迹线
                    var polyline = L.polyline(trackingPath, {
                        color: '#1890ff',
                        weight: 3,
                        opacity: 0.8
                    }).addTo(map);
                }
            }
        }

        // 刷新数据
        function refreshData() {
            loadGPSFromThingSpeak();
        }

        // 回到地图中心
        function centerMap() {
            if (marker) {
                map.setView(marker.getLatLng(), 16);
            }
        }
        
        // 切换轨迹追踪
        function toggleTracking() {
            isTracking = !isTracking;
            if (isTracking) {
                trackingPath = [marker.getPosition()];
                alert('轨迹追踪已开启');
            } else {
                alert('轨迹追踪已关闭');
            }
        }
        
        // 测试ThingSpeak连接（只读取，不上传测试数据）
        function testThingSpeakConnection() {
            console.log('🔧 开始测试ThingSpeak连接...');
            alert('🔧 开始测试ThingSpeak连接，请查看控制台输出');

            // 测试读取API
            console.log('📖 测试读取API...');
            console.log('频道ID:', THINGSPEAK_CHANNEL);
            console.log('读取API密钥:', THINGSPEAK_READ_KEY);

            loadGPSFromThingSpeak();

            // 不再发送测试数据，避免干扰真实GPS数据
            setTimeout(() => {
                console.log('📝 写入API密钥配置正确:', THINGSPEAK_WRITE_KEY);
                console.log('⚠️ 为避免数据污染，测试功能不会上传随机数据');
                console.log('💡 真实GPS数据将由ESP01模块上传');

                alert('✅ ThingSpeak连接测试完成！\n' +
                      '📖 读取功能正常\n' +
                      '⚠️ 已禁用测试数据上传，避免干扰真实GPS数据\n' +
                      '💡 请查看控制台日志了解详情');
            }, 2000);
        }

        // 打开ThingSpeak
        function openThingSpeak() {
            window.open(`https://thingspeak.com/channels/${THINGSPEAK_CHANNEL}`, '_blank');
        }
        


        // 初始化
        window.onload = function() {
            initMap();

            // 从localStorage恢复最后更新时间
            var lastUpdateTime = localStorage.getItem('lastGpsUpdateTime');
            if (lastUpdateTime) {
                document.getElementById('updateTime').textContent = lastUpdateTime;
            } else {
                document.getElementById('updateTime').textContent = '--';
            }

            // 立即加载一次ThingSpeak数据
            loadGPSFromThingSpeak();

            // 每10秒从ThingSpeak获取一次GPS数据
            setInterval(loadGPSFromThingSpeak, 10000);

            console.log('高德地图GPS追踪系统已初始化');
            console.log('正在从ThingSpeak获取ESP01上传的GPS数据...');

            // 添加测试按钮
            addTestButton();
        };

        // 添加测试按钮
        function addTestButton() {
            var testButton = document.createElement('button');
            testButton.innerHTML = '🧪 测试万达导航API';
            testButton.style.position = 'fixed';
            testButton.style.top = '10px';
            testButton.style.right = '10px';
            testButton.style.zIndex = '99999';  // 更高的z-index
            testButton.style.padding = '10px 20px';
            testButton.style.backgroundColor = '#ff4d4f';
            testButton.style.color = 'white';
            testButton.style.border = 'none';
            testButton.style.borderRadius = '4px';
            testButton.style.cursor = 'pointer';
            testButton.style.fontSize = '14px';
            testButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';

            // 防止事件冒泡和默认行为
            testButton.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('🧪 开始测试万达导航API...');
                alert('🚀 开始测试高德地图API调用！请查看控制台日志');

                // 模拟从ThingSpeak获取的导航数据
                var testNavigationData = "WANDA_112.676903_26.881201_112.675797_26.886900";
                console.log('📥 模拟导航数据:', testNavigationData);
                console.log('🔍 这将直接调用高德地图API进行路径规划');

                // 直接调用处理函数
                processNavigationData(testNavigationData);

                return false;
            };

            document.body.appendChild(testButton);
        }

        // 处理导航数据
        function processNavigationData(navigationStr) {
            try {
                console.log('🔍 解析导航数据:', navigationStr);
                console.log('🔍 数据类型:', typeof navigationStr);
                console.log('🔍 数据长度:', navigationStr.length);

                // 解析格式: WANDA_start_lon_start_lat_end_lon_end_lat (经度在前，纬度在后)
                if (navigationStr.startsWith('WANDA_')) {
                    console.log('✅ 检测到WANDA导航数据');
                    var coords = navigationStr.substring(6).split('_'); // 去掉 "WANDA_" 前缀
                    console.log('🔍 分割后的坐标数组:', coords);

                    if (coords.length >= 4) {
                        var startLon = parseFloat(coords[0]); // 起点经度
                        var startLat = parseFloat(coords[1]); // 起点纬度
                        var endLon = parseFloat(coords[2]);   // 终点经度
                        var endLat = parseFloat(coords[3]);   // 终点纬度

                        console.log('📍 万达导航路径 (经度,纬度):', startLon, startLat, '->', endLon, endLat);
                        console.log('🚀 准备调用高德地图API...');

                        // 使用高德地图API显示导航路线
                        showAmapNavigationRoute(startLon, startLat, endLon, endLat);
                    } else {
                        console.error('❌ 坐标数据不足，需要4个坐标值，实际获得:', coords.length);
                    }
                } else {
                    console.log('ℹ️ 不是WANDA导航数据，跳过处理');
                }
            } catch (error) {
                console.error('❌ 导航数据解析失败:', error);
                console.log('原始数据:', navigationStr);
            }
        }

        // 使用高德地图JavaScript API显示导航路线
        function showAmapNavigationRoute(startLon, startLat, endLon, endLat) {
            try {
                console.log('🧠 开始使用自定义路径规划系统...');
                console.log('📍 起点坐标:', startLon, startLat);
                console.log('📍 终点坐标:', endLon, endLat);

                // 清除之前的导航路线
                clearNavigationRoute();

                // 使用自定义路径规划引擎
                var route = CustomRouteEngine.planRoute(startLon, startLat, endLon, endLat);

                if (route && route.waypoints.length > 0) {
                    console.log('✅ 自定义路径规划成功');
                    console.log('📍 路径信息:', route);

                    // 在地图上绘制路径
                    drawCustomRoute(route);

                    // 显示导航信息
                    var distance = (route.distance / 1000).toFixed(1); // 转换为公里
                    var time = route.estimatedTime; // 分钟
                    showNavigationInfo(distance, time);

                    // 显示详细导航指令
                    showNavigationInstructions(route.instructions);

                    // 调整地图视野以显示完整路径
                    fitMapToRoute(route.waypoints);

                    isNavigating = true;
                    console.log('🎯 自定义导航已启动！距离:' + distance + 'km, 时间:' + time + '分钟');

                } else {
                    console.error('❌ 自定义路径规划失败');
                    // 备选方案：显示简单直线路径
                    displaySimpleRoute(startLat, startLon, endLat, endLon);
                }

            } catch (error) {
                console.error('❌ 自定义路径规划调用失败:', error);
                // 备选方案：显示简单直线路径
                displaySimpleRoute(startLat, startLon, endLat, endLon);
            }
        }

        // 在地图上绘制自定义路径
        function drawCustomRoute(route) {
            console.log('🎨 开始绘制自定义路径...');

            if (!map || !route.waypoints || route.waypoints.length < 2) {
                console.error('❌ 无法绘制路径：地图或路径点无效');
                return;
            }

            // 清除之前的路径
            if (routePolyline) {
                map.remove(routePolyline);
                routePolyline = null;
            }

            // 清除之前的标记
            if (startMarker) {
                map.remove(startMarker);
                startMarker = null;
            }
            if (endMarker) {
                map.remove(endMarker);
                endMarker = null;
            }

            // 创建路径点数组
            var pathPoints = [];
            for (var i = 0; i < route.waypoints.length; i++) {
                var point = route.waypoints[i];
                pathPoints.push(new AMap.LngLat(point[0], point[1]));
            }

            // 绘制路径线条
            routePolyline = new AMap.Polyline({
                path: pathPoints,
                strokeColor: '#FF6600',    // 橙色路径
                strokeWeight: 6,
                strokeOpacity: 0.9,
                strokeStyle: 'solid',
                lineJoin: 'round',
                lineCap: 'round'
            });
            map.add(routePolyline);

            // 添加起点标记
            startMarker = new AMap.Marker({
                position: pathPoints[0],
                title: '起点: 当前位置',
                icon: new AMap.Icon({
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iNCIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K',
                    size: new AMap.Size(24, 24)
                })
            });
            map.add(startMarker);

            // 添加终点标记
            endMarker = new AMap.Marker({
                position: pathPoints[pathPoints.length - 1],
                title: '终点: 万达广场',
                icon: new AMap.Icon({
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjRkY0NDQ0Ii8+Cjwvc3ZnPgo=',
                    size: new AMap.Size(24, 24)
                })
            });
            map.add(endMarker);

            // 添加中间路径点标记
            for (var i = 1; i < pathPoints.length - 1; i++) {
                var wayPointMarker = new AMap.Marker({
                    position: pathPoints[i],
                    title: '路径点 ' + i,
                    icon: new AMap.Icon({
                        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjYiIGZpbGw9IiNGRjY2MDAiLz4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjMiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==',
                        size: new AMap.Size(16, 16)
                    })
                });
                map.add(wayPointMarker);
            }

            console.log('✅ 自定义路径绘制完成');
        }

        // 调整地图视野以显示完整路径
        function fitMapToRoute(waypoints) {
            if (!map || !waypoints || waypoints.length === 0) {
                return;
            }

            var bounds = new AMap.Bounds();
            for (var i = 0; i < waypoints.length; i++) {
                bounds.extend(new AMap.LngLat(waypoints[i][0], waypoints[i][1]));
            }

            map.setBounds(bounds, false, [50, 50, 50, 50]); // 添加边距
        }

        // 显示导航指令
        function showNavigationInstructions(instructions) {
            console.log('📋 导航指令:');
            for (var i = 0; i < instructions.length; i++) {
                console.log((i + 1) + '. ' + instructions[i]);
            }

            // 在页面上显示导航指令（可选）
            var instructionText = '🧭 导航指令:\n';
            for (var i = 0; i < instructions.length; i++) {
                instructionText += (i + 1) + '. ' + instructions[i] + '\n';
            }

            // 可以在这里添加UI显示导航指令
            // 暂时使用console输出
            console.log('📋 完整导航指令:\n' + instructionText);
        }

        // 显示简单直线路径（备选方案）
        function displaySimpleRoute(startLat, startLon, endLat, endLon) {
            console.log('📍 显示简单直线路径作为备选方案');

            if (map) {
                // 添加起点和终点标记
                var startMarker = new AMap.Marker({
                    position: new AMap.LngLat(startLon, startLat),
                    title: '起点: 当前位置'
                });
                map.add(startMarker);

                var endMarker = new AMap.Marker({
                    position: new AMap.LngLat(endLon, endLat),
                    title: '终点: 万达广场'
                });
                map.add(endMarker);

                // 添加直线路径
                var polyline = new AMap.Polyline({
                    path: [
                        new AMap.LngLat(startLon, startLat),
                        new AMap.LngLat(endLon, endLat)
                    ],
                    strokeColor: '#3366FF',
                    strokeWeight: 4,
                    strokeOpacity: 0.8
                });
                map.add(polyline);

                // 调整地图视野
                map.setFitView([startMarker, endMarker, polyline]);

                console.log('✅ 简单直线路径已显示');
            }
        }

        // 显示导航路线（备用方案）
        function showNavigationRoute(startLat, startLon, endLat, endLon) {
            try {
                console.log('🗺️ 开始显示导航路线...');

                // 清除之前的导航路线
                clearNavigationRoute();

                // 创建起点和终点
                var startPoint = new AMap.LngLat(startLon, startLat);
                var endPoint = new AMap.LngLat(endLon, endLat);

                console.log('🚗 起点:', startPoint.toString());
                console.log('🏢 终点:', endPoint.toString());

                // 使用高德地图路径规划
                driving.search(startPoint, endPoint, function(status, result) {
                    if (status === 'complete') {
                        console.log('✅ 路径规划成功');
                        console.log('📊 路径信息:', result);

                        // 获取路径信息
                        var route = result.routes[0];
                        var distance = (route.distance / 1000).toFixed(1); // 转换为公里
                        var time = Math.round(route.time / 60); // 转换为分钟

                        // 显示导航信息
                        showNavigationInfo(distance, time);

                        // 调整地图视野以显示完整路径
                        map.setFitView();

                        isNavigating = true;

                    } else {
                        console.error('❌ 路径规划失败:', status);
                        // 备选方案：显示直线路径
                        showStraightLineRoute(startLat, startLon, endLat, endLon);
                    }
                });

            } catch (error) {
                console.error('❌ 显示导航路线失败:', error);
            }
        }

        // 显示直线路径（备选方案）
        function showStraightLineRoute(startLat, startLon, endLat, endLon) {
            console.log('⚠️ 使用直线路径（备选方案）');

            // 创建直线路径
            var lineArr = [
                [startLon, startLat],
                [endLon, endLat]
            ];

            var polyline = new AMap.Polyline({
                path: lineArr,
                strokeColor: "#FF6B35",
                strokeWeight: 6,
                strokeOpacity: 0.8,
                strokeStyle: "dashed"
            });

            map.add(polyline);
            navigationRoute = polyline;

            // 添加起点和终点标记
            startMarker = new AMap.Marker({
                position: [startLon, startLat],
                title: '起点：当前位置',
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png'
                })
            });

            endMarker = new AMap.Marker({
                position: [endLon, endLat],
                title: '终点：酃湖万达广场',
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png'
                })
            });

            map.add([startMarker, endMarker]);

            // 调整地图视野
            map.setFitView([polyline, startMarker, endMarker]);

            // 计算直线距离
            var distance = AMap.GeometryUtil.distance([startLon, startLat], [endLon, endLat]);
            showNavigationInfo((distance / 1000).toFixed(1), '未知');

            isNavigating = true;
        }

        // 显示导航信息
        function showNavigationInfo(distance, time) {
            var infoHtml = `
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #856404;">🗺️ 导航信息</h4>
                    <p style="margin: 5px 0;"><strong>目的地:</strong> 酃湖万达广场</p>
                    <p style="margin: 5px 0;"><strong>距离:</strong> ${distance} 公里</p>
                    <p style="margin: 5px 0;"><strong>预计时间:</strong> ${time} 分钟</p>
                    <button onclick="clearNavigationRoute()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">结束导航</button>
                </div>
            `;

            // 在信息面板中显示导航信息
            var infoPanel = document.querySelector('.info-panel');
            var existingNavInfo = document.getElementById('nav-info');
            if (existingNavInfo) {
                existingNavInfo.remove();
            }

            var navInfoDiv = document.createElement('div');
            navInfoDiv.id = 'nav-info';
            navInfoDiv.innerHTML = infoHtml;
            infoPanel.appendChild(navInfoDiv);
        }

        // 处理串口输入命令
        function handleSerialCommand(command) {
            console.log('📡 收到串口命令:', command);

            if (command.toLowerCase() === 'wanda') {
                console.log('🎯 检测到WANDA导航命令');

                // 获取当前GPS位置
                var currentLat = window.currentGPS ? window.currentGPS.lat : 26.881201;
                var currentLon = window.currentGPS ? window.currentGPS.lon : 112.676903;
                var currentAlt = window.currentGPS ? window.currentGPS.alt : 68.0;

                // 酃湖万达广场坐标 (精确坐标)
                var wandaLat = 26.886900;
                var wandaLon = 112.675797;

                console.log('📍 当前位置:', currentLat, currentLon);
                console.log('🎯 目标位置:', wandaLat, wandaLon);

                // 立即使用高德地图驾车路径规划
                startAmapDrivingNavigation(currentLon, currentLat, wandaLon, wandaLat);

                // 构建导航数据字符串用于上传
                var navigationData = `WANDA_${currentLon}_${currentLat}_${wandaLon}_${wandaLat}`;
                console.log('🗺️ 生成导航数据:', navigationData);

                // 立即上传包含导航指令的GPS数据到ThingSpeak
                uploadNavigationData(currentLat, currentLon, currentAlt, navigationData);

                console.log('✅ WANDA导航指令已上传到ThingSpeak');

                // 显示提示信息
                alert('🎯 WANDA导航已启动！\n目的地：酃湖万达广场\n正在规划驾车路线...\n导航数据已上传到ThingSpeak');

            } else {
                console.log('ℹ️ 未知命令:', command);
            }
        }

        // 启动高德地图驾车导航
        function startAmapDrivingNavigation(startLon, startLat, endLon, endLat) {
            console.log('🚗 启动高德地图驾车导航...');
            console.log('📍 起点:', startLon, startLat);
            console.log('🎯 终点:', endLon, endLat);

            try {
                // 清除之前的导航路线
                clearNavigationRoute();

                console.log('🔍 检查AMap对象:', typeof AMap);
                console.log('🔍 检查map对象:', typeof map);

                if (typeof AMap !== 'undefined' && map) {
                    console.log('✅ AMap和map对象都存在，加载驾车路径规划插件...');

                    // 使用API v2.0的插件加载方式
                    AMap.plugin('AMap.Driving', function() {
                        console.log('🔧 创建AMap.Driving实例...');
                        driving = new AMap.Driving({
                            map: map,
                            showTraffic: true,  // 显示实时路况
                            hideMarkers: false, // 显示起终点标记
                            autoFitView: true   // 自动调整视野
                        });

                        console.log('✅ AMap.Driving实例创建成功');
                        console.log('🚀 开始驾车路径规划...');

                        // 执行驾车路径规划
                        driving.search(
                            new AMap.LngLat(startLon, startLat),
                            new AMap.LngLat(endLon, endLat),
                            function(status, result) {
                                console.log('📡 高德驾车路径规划回调 - 状态:', status);
                                console.log('📡 回调结果:', result);

                                if (status === 'complete') {
                                    console.log('✅ 驾车路径规划成功！');

                                    // 获取路径信息
                                    if (result.routes && result.routes.length > 0) {
                                        var route = result.routes[0];
                                        var distance = (route.distance / 1000).toFixed(1); // 转换为公里
                                        var duration = Math.round(route.time / 60); // 转换为分钟

                                        console.log('📏 路径距离:', distance + 'km');
                                        console.log('⏱️ 预计时间:', duration + '分钟');

                                        // 显示导航信息
                                        showNavigationInfo(distance, duration);

                                        // 设置导航状态
                                        isNavigating = true;

                                        alert('✅ 导航路线规划成功！\n距离: ' + distance + 'km\n预计时间: ' + duration + '分钟');
                                    } else {
                                        console.log('⚠️ 没有找到路径');
                                        alert('⚠️ 没有找到合适的路径');
                                    }

                                } else if (status === 'error') {
                                    console.error('❌ 驾车路径规划失败:', result);
                                    alert('❌ 路径规划失败: ' + (result.info || '未知错误'));
                                } else {
                                    console.log('⚠️ 路径规划状态:', status, result);
                                    alert('⚠️ 路径规划状态: ' + status);
                                }
                            }
                        );
                    }); // 结束AMap.plugin回调

                } else {
                    console.error('❌ AMap对象或map对象不存在');
                    console.log('AMap类型:', typeof AMap);
                    console.log('map类型:', typeof map);
                    alert('❌ 地图API未正确加载，无法进行导航');
                }

            } catch (error) {
                console.error('❌ 驾车导航启动失败:', error);
                alert('❌ 导航启动失败: ' + error.message);
            }
        }

        // 上传导航数据到ThingSpeak
        function uploadNavigationData(lat, lon, alt, navigationCmd) {
            console.log('📤 上传导航数据到ThingSpeak...');

            // 构建ThingSpeak URL
            var thingSpeakURL = 'https://api.thingspeak.com/update?api_key=LU22ZUP4ZTFK4IY9';
            thingSpeakURL += '&field1=' + lat.toFixed(6);
            thingSpeakURL += '&field2=' + lon.toFixed(6);

            // Field3格式：高度|导航指令
            var field3Value = alt.toFixed(1) + '|' + navigationCmd;
            thingSpeakURL += '&field3=' + encodeURIComponent(field3Value);

            console.log('🔗 上传URL:', thingSpeakURL);

            // 发送数据
            fetch(thingSpeakURL)
                .then(response => response.text())
                .then(result => {
                    if (result && result !== '0') {
                        console.log('✅ 导航数据上传成功，Entry ID:', result);
                    } else {
                        console.error('❌ 导航数据上传失败');
                    }
                })
                .catch(error => {
                    console.error('❌ 上传失败:', error);
                });
        }

        // 清除导航路线
        function clearNavigationRoute() {
            console.log('🧹 清除导航路线');

            // 清除Leaflet地图的路径线条
            if (routePolyline) {
                map.removeLayer(routePolyline);
                routePolyline = null;
            }

            // 清除起点标记
            if (startMarker) {
                map.removeLayer(startMarker);
                startMarker = null;
            }

            // 清除终点标记
            if (endMarker) {
                map.removeLayer(endMarker);
                endMarker = null;
            }

            // 清除导航信息显示
            var navInfo = document.getElementById('nav-info');
            if (navInfo) {
                navInfo.remove();
            }

            isNavigating = false;
            console.log('✅ 导航路线已清除');
        }

        // 模拟串口输入（用于测试）
        async function simulateSerialInput() {
            console.log('🧪 启动WANDA导航测试...');

            // 使用OSRM规划从当前位置到万达广场的路径
            const startLat = DEFAULT_LAT;
            const startLon = DEFAULT_LON;
            const endLat = 26.886900;  // 酃湖万达广场纬度
            const endLon = 112.675797; // 酃湖万达广场经度

            console.log(`📍 起点: ${startLat}, ${startLon}`);
            console.log(`🎯 终点: ${endLat}, ${endLon}`);

            const routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon);

            if (routeData) {
                displayOSRMRoute(routeData);
                alert(`✅ WANDA导航启动成功！\n距离: ${(routeData.distance/1000).toFixed(1)}km\n时间: ${Math.round(routeData.duration/60)}分钟\n使用OSRM免费路径规划`);
            } else {
                alert('❌ 路径规划失败，请检查网络连接');
            }
        }

        // 测试自定义路径规划系统
        async function testCustomRoute() {
            console.log('🧠 测试OSRM路径规划系统...');

            // 测试路径：从衡阳师范学院到万达广场
            const startLat = DEFAULT_LAT;   // 衡阳师范学院纬度
            const startLon = DEFAULT_LON;   // 衡阳师范学院经度
            const endLat = 26.886900;         // 酃湖万达广场纬度
            const endLon = 112.675797;        // 酃湖万达广场经度

            console.log('📍 测试起点:', startLat, startLon);
            console.log('📍 测试终点:', endLat, endLon);

            // 调用OSRM路径规划
            const routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon);

            if (routeData) {
                displayOSRMRoute(routeData);
                alert('🧠 OSRM路径规划测试完成！\n' +
                      '路径: 衡阳师范学院 → 万达广场\n' +
                      '距离: ' + (routeData.distance / 1000).toFixed(1) + 'km\n' +
                      '时间: ' + Math.round(routeData.duration / 60) + '分钟\n' +
                      '使用OSRM免费路径规划，无需API密钥！\n' +
                      '请查看控制台日志和地图显示。');
            } else {
                alert('❌ 路径规划失败，请检查网络连接');
            }
        }

        // 添加坐标输入功能
        async function planCustomRoute() {
            var startCoords = prompt('请输入起点坐标 (格式: 纬度,经度)\n例如: 26.881201,112.676903');
            if (!startCoords) return;

            var endCoords = prompt('请输入终点坐标 (格式: 纬度,经度)\n例如: 26.886900,112.675797');
            if (!endCoords) return;

            try {
                var start = startCoords.split(',');
                var end = endCoords.split(',');

                if (start.length !== 2 || end.length !== 2) {
                    alert('❌ 坐标格式错误！请使用格式：纬度,经度');
                    return;
                }

                var startLat = parseFloat(start[0].trim());
                var startLon = parseFloat(start[1].trim());
                var endLat = parseFloat(end[0].trim());
                var endLon = parseFloat(end[1].trim());

                if (isNaN(startLat) || isNaN(startLon) || isNaN(endLat) || isNaN(endLon)) {
                    alert('❌ 坐标必须是有效的数字！');
                    return;
                }

                console.log('🎯 用户自定义OSRM路径规划');
                console.log('📍 起点:', startLat, startLon);
                console.log('📍 终点:', endLat, endLon);

                // 调用OSRM路径规划
                const routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon);

                if (routeData) {
                    displayOSRMRoute(routeData);
                    alert('✅ 自定义路径规划完成！\n' +
                          '距离: ' + (routeData.distance / 1000).toFixed(1) + 'km\n' +
                          '时间: ' + Math.round(routeData.duration / 60) + '分钟\n' +
                          '使用OSRM免费服务');
                } else {
                    alert('❌ 路径规划失败，请检查坐标和网络连接');
                }

            } catch (error) {
                console.error('❌ 坐标解析失败:', error);
                alert('❌ 坐标解析失败，请检查输入格式！');
            }
        }

        // 新增导航功能（整合自OpenStreetMap导航版本）

        // 导航到高铁站
        async function navigateToGaotie() {
            await navigateToDestination('gaotie');
        }

        // 导航到医院
        async function navigateToHospital() {
            await navigateToDestination('yiyuan');
        }

        // 导航到火车站
        async function navigateToStation() {
            await navigateToDestination('huochezhan');
        }

        // 通用导航函数
        async function navigateToDestination(destinationKey) {
            const destination = DESTINATIONS[destinationKey];
            if (!destination) {
                alert('❌ 目的地不存在');
                return;
            }

            console.log(`🎯 启动${destination.name}导航...`);

            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            try {
                const routeData = await planRouteWithOSRM(currentLat, currentLon, destination.lat, destination.lon);

                if (routeData) {
                    displayOSRMRoute(routeData);

                    // 构建导航数据字符串用于上传
                    const navigationData = `${destinationKey.toUpperCase()}_${currentLon}_${currentLat}_${destination.lon}_${destination.lat}`;
                    console.log('🗺️ 生成导航数据:', navigationData);

                    // 上传导航数据到ThingSpeak
                    uploadNavigationData(currentLat, currentLon, 68.0, navigationData);

                    alert(`🎯 ${destination.name}导航已启动！\n` +
                          `距离: ${(routeData.distance/1000).toFixed(1)}km\n` +
                          `时间: ${Math.round(routeData.duration/60)}分钟\n` +
                          `导航数据已上传到ThingSpeak`);
                } else {
                    alert(`❌ ${destination.name}导航失败，请检查网络连接`);
                }
            } catch (error) {
                console.error(`❌ ${destination.name}导航启动失败:`, error);
                alert(`❌ ${destination.name}导航启动失败: ${error.message}`);
            }
        }
    </script>
</body>
</html>
