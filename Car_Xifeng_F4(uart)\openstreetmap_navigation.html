<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>STM32F407VET6 GPS追踪导航系统</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: #2E8B57;
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .info-panel {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item {
            text-align: center;
            flex: 1;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #2E8B57;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        #map {
            width: calc(100% - 20px);
            height: 500px;
            margin: 10px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .btn {
            background-color: #2E8B57;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #228B22;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .navigation-info {
            background: #e8f5e8;
            border: 1px solid #2E8B57;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: none;
        }
        
        .navigation-info h3 {
            margin: 0 0 10px 0;
            color: #2E8B57;
        }
        
        .route-instructions {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: none;
        }
        
        .route-instructions h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .instruction-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .instruction-item:last-child {
            border-bottom: none;
        }
        
        .instruction-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background-color: #2E8B57;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .log-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .log-warning { color: #ffc107; }

        /* 当前导航步骤高亮 */
        .instruction-item.current-step {
            background: #FF4444 !important;
            color: white !important;
            border-left: 5px solid #FF0000;
            animation: pulse 1s infinite;
        }

        .instruction-item.current-step .instruction-icon {
            background: white !important;
            color: #FF4444 !important;
        }

        /* 导航提示动画 */
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateX(-50%) translateY(0); opacity: 1; }
            to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ STM32F407VET6 GPS追踪导航系统</h1>
        <p>步行路径规划 | 实时路线更新 | 防定位乱飘</p>
    </div>

    <div class="info-panel">
        <div class="status">
            <div class="status-item">
                <div class="status-value" id="currentLat">--</div>
                <div class="status-label">纬度</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="currentLon">--</div>
                <div class="status-label">经度</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="currentAlt">--</div>
                <div class="status-label">海拔(m)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="updateTime">--</div>
                <div class="status-label">更新时间</div>
            </div>
        </div>
    </div>

    <div id="map"></div>

    <div class="navigation-info" id="navigationInfo">
        <h3>🧭 导航信息</h3>
        <p><strong>距离:</strong> <span id="routeDistance">--</span></p>
        <p><strong>预计时间:</strong> <span id="routeTime">--</span></p>
        <p><strong>路径规划:</strong> <span id="routeProvider">--</span></p>
    </div>

    <div class="route-instructions" id="routeInstructions">
        <h3>📋 导航指令</h3>
        <div id="instructionsList"></div>
    </div>

    <div class="controls">
        <button class="btn" onclick="refreshGPSData()">🔄 刷新GPS数据</button>
        <button class="btn" onclick="centerMap()">📍 回到中心</button>
        <button class="btn" onclick="startWandaNavigation()">🎯 万达导航</button>
        <button class="btn" onclick="planCustomRoute()">🗺️ 自定义路径</button>
        <button class="btn btn-secondary" onclick="clearRoute()">🧹 清除路径</button>
        <button class="btn btn-secondary" onclick="toggleInstructions()">📋 显示/隐藏指令</button>
    </div>

    <div class="log-panel" id="logPanel">
        <div class="log-entry log-info">🚀 系统初始化中...</div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // 全局变量
        let map;
        let currentMarker;
        let routeLayer;
        let isNavigating = false;
        let currentDestination = null;
        let lastRouteUpdate = 0;
        const ROUTE_UPDATE_INTERVAL = 30000; // 30秒更新一次路线
        let currentRouteSteps = [];
        let currentStepIndex = 0;
        let lastStepCheckTime = 0;
        
        // ThingSpeak配置 - 只读取，不上传
        const THINGSPEAK_CHANNEL = '3014831';
        const THINGSPEAK_READ_KEY = 'LU22ZUP42TFK4IY9';  // 读取API密钥
        const THINGSPEAK_WRITE_KEY = 'V64RR7CZJ9Z4O7ED'; // 写入API密钥（用于测试）
        
        // 衡阳师范学院默认坐标
        const DEFAULT_LAT = 26.8812;
        const DEFAULT_LON = 112.6769;

        // 衡阳师范学院出入口坐标（根据地图分析）
        const CAMPUS_EXITS = [
            { name: '东门出口', lat: 26.8825, lon: 112.6785, description: '主要出入口，通往来雁路' },
            { name: '南门出口', lat: 26.8795, lon: 112.6775, description: '南侧出入口' },
            { name: '北门出口', lat: 26.8835, lon: 112.6760, description: '北侧出入口' }
        ];

        // 万达广场坐标
        const WANDA_LAT = 26.8845;
        const WANDA_LON = 112.6720;
        
        // 预设目的地
        const DESTINATIONS = {
            'wanda': { lat: 26.8869, lon: 112.6758, name: '酃湖万达广场' },
            'gaotie': { lat: 26.8945, lon: 112.6123, name: '衡阳东高铁站' },
            'yiyuan': { lat: 26.8756, lon: 112.6234, name: '南华大学附属第一医院' },
            'huochezhan': { lat: 26.8834, lon: 112.6167, name: '衡阳火车站' }
        };
        
        // 路径规划服务配置（免费API）
        const ROUTING_SERVICES = [
            {
                name: 'OSRM',
                url: 'https://router.project-osrm.org/route/v1/driving/',
                description: '开源路径规划服务'
            },
            {
                name: 'GraphHopper',
                url: 'https://graphhopper.com/api/1/route',
                description: 'GraphHopper免费API',
                key: '' // 可选：如果有API key可以填入
            }
        ];

        // GPS数据滤波器 - 防止定位乱飘
        class GPSFilter {
            constructor() {
                this.history = [];
                this.maxHistory = 3;  // 减少历史记录，提高响应速度
                this.maxJumpDistance = 0.002; // 约200米，更严格的过滤
                this.lastValidPosition = null;
                this.consecutiveValidCount = 0;
            }

            filter(lat, lon) {
                // 基本范围检查
                if (lat < -90 || lat > 90 || lon < -180 || lon > 180 || lat === 0 || lon === 0) {
                    log(`⚠️ GPS坐标无效: ${lat}, ${lon}`, 'warning');
                    return this.lastValidPosition;
                }

                // 初始化
                if (!this.lastValidPosition) {
                    this.lastValidPosition = { lat, lon };
                    this.history.push({ lat, lon });
                    this.consecutiveValidCount = 1;
                    log(`📍 GPS滤波器初始化: ${lat.toFixed(6)}, ${lon.toFixed(6)}`, 'info');
                    return { lat, lon };
                }

                // 计算距离
                const latDiff = lat - this.lastValidPosition.lat;
                const lonDiff = lon - this.lastValidPosition.lon;
                const distance = Math.sqrt(latDiff * latDiff + lonDiff * lonDiff);

                // 检查异常跳跃
                if (distance > this.maxJumpDistance) {
                    log(`⚠️ GPS异常跳跃被过滤: 距离 ${(distance * 111000).toFixed(0)}米`, 'warning');
                    this.consecutiveValidCount = 0;
                    return this.lastValidPosition; // 返回上次有效位置
                }

                // 连续有效计数
                this.consecutiveValidCount++;

                // 添加到历史记录
                this.history.push({ lat, lon });
                if (this.history.length > this.maxHistory) {
                    this.history.shift();
                }

                // 只有连续收到有效数据才进行平滑处理
                if (this.consecutiveValidCount >= 2) {
                    // 计算加权平均（最新数据权重更大）
                    let totalWeight = 0;
                    let weightedLat = 0;
                    let weightedLon = 0;

                    for (let i = 0; i < this.history.length; i++) {
                        const weight = i + 1; // 越新的数据权重越大
                        weightedLat += this.history[i].lat * weight;
                        weightedLon += this.history[i].lon * weight;
                        totalWeight += weight;
                    }

                    const avgLat = weightedLat / totalWeight;
                    const avgLon = weightedLon / totalWeight;

                    this.lastValidPosition = { lat: avgLat, lon: avgLon };
                } else {
                    // 前几个数据直接使用
                    this.lastValidPosition = { lat, lon };
                }

                return this.lastValidPosition;
            }
        }

        const gpsFilter = new GPSFilter();

        // 日志函数
        function log(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 初始化地图
        function initMap() {
            log('🗺️ 初始化OpenStreetMap...', 'info');
            
            map = L.map('map').setView([DEFAULT_LAT, DEFAULT_LON], 16);
            
            // 添加多个地图源，提高加载成功率
            const mapSources = [
                {
                    name: 'OpenStreetMap',
                    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                },
                {
                    name: 'OpenStreetMap DE',
                    url: 'https://{s}.tile.openstreetmap.de/{z}/{x}/{y}.png',
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                },
                {
                    name: 'CartoDB Positron',
                    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
                }
            ];

            // 尝试加载地图源
            let mapLoaded = false;
            for (let i = 0; i < mapSources.length && !mapLoaded; i++) {
                try {
                    const tileLayer = L.tileLayer(mapSources[i].url, {
                        attribution: mapSources[i].attribution,
                        maxZoom: 19,
                        timeout: 10000,  // 10秒超时
                        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
                    });

                    tileLayer.addTo(map);
                    log(`✅ 地图源加载成功: ${mapSources[i].name}`, 'success');
                    mapLoaded = true;
                } catch (error) {
                    log(`⚠️ 地图源加载失败: ${mapSources[i].name} - ${error.message}`, 'warning');
                }
            }

            if (!mapLoaded) {
                log('❌ 所有地图源加载失败，使用离线模式', 'error');
            }
            
            // 创建当前位置标记
            currentMarker = L.marker([DEFAULT_LAT, DEFAULT_LON])
                .addTo(map)
                .bindPopup('📍 当前GPS位置<br>衡阳师范学院')
                .openPopup();
            
            log('✅ OpenStreetMap初始化完成', 'success');
        }
        
        // 从ThingSpeak获取GPS数据
        async function loadGPSFromThingSpeak() {
            try {
                log('📡 正在从ThingSpeak获取GPS数据...', 'info');

                // 尝试多种API方式获取数据
                let data = null;
                let success = false;

                // 方式1: 使用读取API密钥
                try {
                    const url1 = `https://api.thingspeak.com/channels/${THINGSPEAK_CHANNEL}/feeds.json?api_key=${THINGSPEAK_READ_KEY}&results=1`;
                    const response1 = await fetch(url1);
                    data = await response1.json();
                    if (data && data.feeds && data.feeds.length > 0) {
                        success = true;
                        log('✅ 使用读取API密钥成功获取数据', 'success');
                    }
                } catch (e) {
                    log('⚠️ 读取API密钥方式失败，尝试公开API', 'warning');
                }

                // 方式2: 使用公开API（如果方式1失败）
                if (!success) {
                    try {
                        const url2 = `https://api.thingspeak.com/channels/${THINGSPEAK_CHANNEL}/feeds.json?results=1`;
                        const response2 = await fetch(url2);
                        data = await response2.json();
                        if (data && data.feeds && data.feeds.length > 0) {
                            success = true;
                            log('✅ 使用公开API成功获取数据', 'success');
                        }
                    } catch (e) {
                        log('❌ 公开API也失败', 'error');
                    }
                }

                if (success && data.feeds && data.feeds.length > 0) {
                    const feed = data.feeds[0];
                    const rawLat = parseFloat(feed.field1);
                    const rawLon = parseFloat(feed.field2);
                    const alt = parseFloat(feed.field3) || 0;

                    log(`📍 ThingSpeak数据: 纬度=${rawLat}, 经度=${rawLon}, 海拔=${alt}, 时间=${feed.created_at}`, 'info');

                    // 检查数据有效性
                    if (!isNaN(rawLat) && !isNaN(rawLon) && rawLat !== 0 && rawLon !== 0) {
                        updateGPSDisplay(rawLat, rawLon, alt, feed.created_at);
                        log(`✅ GPS数据已更新: ${rawLat.toFixed(6)}, ${rawLon.toFixed(6)}`, 'success');
                    } else {
                        log('⚠️ ThingSpeak数据无效，使用默认位置', 'warning');
                        updateGPSDisplay(DEFAULT_LAT, DEFAULT_LON, alt, feed.created_at || new Date().toISOString());
                    }
                } else {
                    log('⚠️ 未获取到有效ThingSpeak数据，使用默认位置', 'warning');
                    updateGPSDisplay(DEFAULT_LAT, DEFAULT_LON, 0, new Date().toISOString());
                }
            } catch (error) {
                log(`❌ 获取ThingSpeak数据失败: ${error.message}`, 'error');
                updateGPSDisplay(DEFAULT_LAT, DEFAULT_LON, 0, new Date().toISOString());
            }
        }
        
        // 更新GPS显示
        function updateGPSDisplay(lat, lon, alt, timestamp) {
            document.getElementById('currentLat').textContent = lat.toFixed(6);
            document.getElementById('currentLon').textContent = lon.toFixed(6);
            document.getElementById('currentAlt').textContent = alt.toFixed(1);
            document.getElementById('updateTime').textContent = new Date(timestamp).toLocaleTimeString();

            // 更新地图标记
            currentMarker.setLatLng([lat, lon]);
            currentMarker.setPopupContent(`📍 当前GPS位置<br>纬度: ${lat.toFixed(6)}<br>经度: ${lon.toFixed(6)}<br>海拔: ${alt.toFixed(1)}m`);

            // 更新当前位置
            currentPosition = { lat: lat, lng: lon };

            // 如果正在导航，检查是否需要更新路线和导航指令
            updateRouteIfNeeded();
            updateNavigationInstructions();
        }

        // 计算两点间距离（米）
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 智能选择最佳出入口
        function findBestExit(currentLat, currentLon, destinationLat, destinationLon) {
            let bestExit = null;
            let shortestDistance = Infinity;

            for (const exit of CAMPUS_EXITS) {
                // 计算从出入口到目的地的距离
                const distanceToDestination = calculateDistance(exit.lat, exit.lon, destinationLat, destinationLon);

                if (distanceToDestination < shortestDistance) {
                    shortestDistance = distanceToDestination;
                    bestExit = exit;
                }
            }

            log(`🚪 选择最佳出入口: ${bestExit.name} (距离目的地${(shortestDistance/1000).toFixed(2)}km)`, 'info');
            return bestExit;
        }

        // 使用OSRM进行步行路径规划
        async function planRouteWithOSRM(startLat, startLon, endLat, endLon) {
            try {
                log(`🚶 使用OSRM进行步行路径规划: (${startLat.toFixed(6)}, ${startLon.toFixed(6)}) → (${endLat.toFixed(6)}, ${endLon.toFixed(6)})`, 'info');

                // 使用foot模式进行步行导航
                const url = `https://router.project-osrm.org/route/v1/foot/${startLon},${startLat};${endLon},${endLat}?overview=full&geometries=geojson&steps=true`;
                log(`🌐 OSRM请求URL: ${url}`, 'info');

                const response = await fetch(url);
                const data = await response.json();

                log(`📊 OSRM响应: ${JSON.stringify(data)}`, 'info');

                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                    const route = data.routes[0];
                    log(`✅ OSRM步行路径规划成功: ${(route.distance/1000).toFixed(2)}km, ${Math.round(route.duration/60)}分钟`, 'success');
                    log(`📍 路径坐标点数量: ${route.geometry.coordinates.length}`, 'info');

                    return {
                        coordinates: route.geometry.coordinates,
                        distance: route.distance,
                        duration: route.duration,
                        steps: route.legs[0].steps,
                        provider: 'OSRM步行'
                    };
                } else {
                    log(`❌ OSRM返回错误: ${data.message || data.code || '未知错误'}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ OSRM步行路径规划失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 使用GraphHopper进行路径规划（备选方案）
        async function planRouteWithGraphHopper(startLat, startLon, endLat, endLon) {
            try {
                log('🗺️ 使用GraphHopper进行路径规划...', 'info');

                // GraphHopper免费API（有限制）
                const url = `https://graphhopper.com/api/1/route?point=${startLat},${startLon}&point=${endLat},${endLon}&vehicle=car&locale=zh&calc_points=true&debug=true&elevation=false&points_encoded=false`;

                const response = await fetch(url);
                const data = await response.json();

                if (data.paths && data.paths.length > 0) {
                    const path = data.paths[0];
                    log(`✅ GraphHopper路径规划成功: ${(path.distance/1000).toFixed(1)}km, ${Math.round(path.time/60000)}分钟`, 'success');

                    return {
                        coordinates: path.points.coordinates.map(coord => [coord[0], coord[1]]),
                        distance: path.distance,
                        duration: path.time / 1000,
                        instructions: path.instructions,
                        provider: 'GraphHopper'
                    };
                } else {
                    throw new Error('GraphHopper返回无效路径');
                }
            } catch (error) {
                log(`❌ GraphHopper路径规划失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 智能路径规划（尝试多个服务）
        async function planRoute(startLat, startLon, endLat, endLon) {
            log(`🎯 开始步行路径规划: (${startLat.toFixed(6)}, ${startLon.toFixed(6)}) → (${endLat.toFixed(6)}, ${endLon.toFixed(6)})`, 'info');

            // 首先尝试OSRM（最可靠的免费服务）
            let routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon);

            // 如果OSRM失败，尝试GraphHopper
            if (!routeData) {
                log('⚠️ OSRM失败，尝试GraphHopper...', 'warning');
                routeData = await planRouteWithGraphHopper(startLat, startLon, endLat, endLon);
            }

            // 如果都失败，使用直线路径作为备选
            if (!routeData) {
                log('⚠️ 所有路径规划服务失败，使用直线路径', 'warning');
                routeData = createStraightLineRoute(startLat, startLon, endLat, endLon);
            }

            return routeData;
        }

        // 实时更新路线
        async function updateRouteIfNeeded() {
            if (!isNavigating || !currentDestination) return;

            const now = Date.now();
            if (now - lastRouteUpdate < ROUTE_UPDATE_INTERVAL) return;

            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            // 检查是否位置有显著变化
            const lastLat = currentPosition ? currentPosition.lat : DEFAULT_LAT;
            const lastLon = currentPosition ? currentPosition.lng : DEFAULT_LON;
            const distance = calculateDistance(currentLat, currentLon, lastLat, lastLon);

            // 如果移动距离超过50米，重新规划路线
            if (distance > 0.05) { // 约50米
                log('🔄 位置变化显著，重新规划路线...', 'info');

                const routeData = await planRoute(currentLat, currentLon, currentDestination.lat, currentDestination.lon);

                if (routeData) {
                    displayRoute(routeData);
                    log(`✅ 路线已更新！新距离: ${(routeData.distance/1000).toFixed(2)}km`, 'success');
                }

                lastRouteUpdate = now;
            }
        }

        // 实时导航指令更新
        function updateNavigationInstructions() {
            if (!isNavigating || !currentRouteSteps.length || !currentPosition) return;

            const now = Date.now();
            if (now - lastStepCheckTime < 5000) return; // 每5秒检查一次

            const currentLat = currentPosition.lat;
            const currentLon = currentPosition.lng;

            // 检查是否接近下一个转向点
            for (let i = currentStepIndex; i < currentRouteSteps.length; i++) {
                const step = currentRouteSteps[i];
                if (step.maneuver && step.maneuver.location) {
                    const stepLat = step.maneuver.location[1];
                    const stepLon = step.maneuver.location[0];
                    const distance = calculateDistance(currentLat, currentLon, stepLat, stepLon);

                    // 如果距离转向点小于30米，显示指令
                    if (distance < 0.03) { // 约30米
                        if (i !== currentStepIndex) {
                            currentStepIndex = i;
                            showCurrentInstruction(step, distance);
                        }
                        break;
                    }
                }
            }

            lastStepCheckTime = now;
        }

        // 显示当前导航指令
        function showCurrentInstruction(step, distance) {
            const instruction = step.maneuver ? step.maneuver.instruction : step.instruction || '继续前进';
            const distanceText = distance < 0.01 ? '即将' : `${Math.round(distance * 1000)}米后`;

            // 更新指令面板
            highlightCurrentStep(currentStepIndex);

            // 显示语音提示
            const message = `${distanceText}${instruction}`;
            log(`🧭 导航提示: ${message}`, 'info');

            // 如果浏览器支持语音合成，播放语音提示
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(message);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }

            // 显示弹窗提示
            showNavigationAlert(message);
        }

        // 高亮当前步骤
        function highlightCurrentStep(stepIndex) {
            const instructionsList = document.getElementById('instructionsList');
            if (!instructionsList) return;

            // 清除之前的高亮
            const items = instructionsList.querySelectorAll('.instruction-item');
            items.forEach(item => item.classList.remove('current-step'));

            // 高亮当前步骤
            if (items[stepIndex]) {
                items[stepIndex].classList.add('current-step');
                items[stepIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 显示导航提示弹窗
        function showNavigationAlert(message) {
            // 创建提示框
            const alertDiv = document.createElement('div');
            alertDiv.className = 'navigation-alert';
            alertDiv.innerHTML = `
                <div class="alert-content">
                    <span class="alert-icon">🧭</span>
                    <span class="alert-text">${message}</span>
                </div>
            `;

            // 添加样式
            alertDiv.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #FF4444;
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                z-index: 10000;
                font-size: 16px;
                font-weight: bold;
                animation: slideDown 0.3s ease-out;
            `;

            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.style.animation = 'slideUp 0.3s ease-in';
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.parentNode.removeChild(alertDiv);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // 创建直线路径（备选方案）
        function createStraightLineRoute(startLat, startLon, endLat, endLon) {
            const distance = calculateDistance(startLat, startLon, endLat, endLon);
            const duration = distance / 35 * 3600; // 假设35km/h平均速度

            return {
                coordinates: [[startLon, startLat], [endLon, endLat]],
                distance: distance * 1000,
                duration: duration,
                steps: [
                    { instruction: '从起点出发', distance: distance * 1000 },
                    { instruction: '直行到达目的地', distance: 0 }
                ],
                provider: '直线路径'
            };
        }

        // 计算两点间距离（Haversine公式）
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 在地图上显示路径
        function displayRoute(routeData) {
            // 清除之前的路径
            if (routeLayer) {
                map.removeLayer(routeLayer);
            }

            // 转换坐标格式（经度,纬度 → 纬度,经度）
            const latLngs = routeData.coordinates.map(coord => [coord[1], coord[0]]);

            // 创建路径线 - 使用更醒目的颜色和样式
            routeLayer = L.polyline(latLngs, {
                color: '#FF4444',        // 鲜红色，非常醒目
                weight: 8,               // 更粗的线条
                opacity: 1.0,            // 完全不透明
                lineCap: 'round',        // 圆形线帽
                lineJoin: 'round'        // 圆形连接
            }).addTo(map);

            // 添加路径动画效果
            let offset = 0;
            const animatePath = () => {
                offset += 2;
                if (routeLayer) {
                    routeLayer.setStyle({
                        dashArray: `20, 10`,
                        dashOffset: offset
                    });
                }
            };
            setInterval(animatePath, 100); // 每100ms更新一次动画

            // 添加起点和终点标记
            const startPoint = latLngs[0];
            const endPoint = latLngs[latLngs.length - 1];

            L.marker(startPoint, {
                icon: L.divIcon({
                    className: 'start-marker',
                    html: '🚗',
                    iconSize: [30, 30]
                })
            }).addTo(map).bindPopup('🚗 起点');

            L.marker(endPoint, {
                icon: L.divIcon({
                    className: 'end-marker',
                    html: '🏁',
                    iconSize: [30, 30]
                })
            }).addTo(map).bindPopup('🏁 终点');

            // 调整地图视野以显示完整路径
            map.fitBounds(routeLayer.getBounds(), { padding: [20, 20] });

            // 显示导航信息
            showNavigationInfo(routeData);

            log(`✅ 路径显示完成，使用${routeData.provider}服务`, 'success');
        }

        // 显示导航信息
        function showNavigationInfo(routeData) {
            const navigationInfo = document.getElementById('navigationInfo');
            const routeDistance = document.getElementById('routeDistance');
            const routeTime = document.getElementById('routeTime');
            const routeProvider = document.getElementById('routeProvider');

            routeDistance.textContent = `${(routeData.distance / 1000).toFixed(2)} 公里`;
            routeTime.textContent = `${Math.round(routeData.duration / 60)} 分钟`;
            routeProvider.textContent = routeData.provider;

            navigationInfo.style.display = 'block';

            // 保存路线步骤用于实时导航
            currentRouteSteps = routeData.steps || routeData.instructions || [];
            currentStepIndex = 0;
            lastStepCheckTime = 0;

            // 显示详细指令
            if (currentRouteSteps.length > 0) {
                showRouteInstructions(currentRouteSteps);
                log(`🧭 导航指令已准备，共${currentRouteSteps.length}个步骤`, 'info');
            }
        }

        // 显示路径指令
        function showRouteInstructions(instructions) {
            const instructionsList = document.getElementById('instructionsList');
            instructionsList.innerHTML = '';

            instructions.forEach((instruction, index) => {
                const item = document.createElement('div');
                item.className = 'instruction-item';

                const icon = document.createElement('div');
                icon.className = 'instruction-icon';
                icon.textContent = index + 1;

                const text = document.createElement('div');
                text.textContent = instruction.instruction || instruction.text || `步骤 ${index + 1}`;

                item.appendChild(icon);
                item.appendChild(text);
                instructionsList.appendChild(item);
            });

            document.getElementById('routeInstructions').style.display = 'block';
        }

        // 控制函数
        async function refreshGPSData() {
            log('🔄 手动刷新GPS数据...', 'info');
            await loadGPSFromThingSpeak();
        }

        function centerMap() {
            const lat = parseFloat(document.getElementById('currentLat').textContent);
            const lon = parseFloat(document.getElementById('currentLon').textContent);

            if (!isNaN(lat) && !isNaN(lon)) {
                map.setView([lat, lon], 16);
                log('📍 地图已居中到当前GPS位置', 'info');
            }
        }

        async function startWandaNavigation() {
            log('🎯 启动万达广场智能导航...', 'info');

            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            // 智能选择最佳出入口
            const bestExit = findBestExit(currentLat, currentLon, WANDA_LAT, WANDA_LON);

            log(`📍 当前位置: (${currentLat.toFixed(6)}, ${currentLon.toFixed(6)})`, 'info');
            log(`🚪 选择出入口: ${bestExit.name} - ${bestExit.description}`, 'info');
            log(`🎯 目标: 万达广场 (${WANDA_LAT.toFixed(6)}, ${WANDA_LON.toFixed(6)})`, 'info');

            // 分段路径规划：当前位置 → 出入口 → 万达广场
            try {
                // 第一段：当前位置到出入口
                log('🚶 规划第一段路径：当前位置 → 校园出入口', 'info');
                const routeToExit = await planRoute(currentLat, currentLon, bestExit.lat, bestExit.lon);

                // 第二段：出入口到万达广场
                log('🚶 规划第二段路径：校园出入口 → 万达广场', 'info');
                const routeToWanda = await planRoute(bestExit.lat, bestExit.lon, WANDA_LAT, WANDA_LON);

                if (routeToExit && routeToWanda) {
                    // 合并两段路径
                    const combinedRoute = {
                        coordinates: [...routeToExit.coordinates, ...routeToWanda.coordinates],
                        distance: routeToExit.distance + routeToWanda.distance,
                        duration: routeToExit.duration + routeToWanda.duration,
                        steps: [...routeToExit.steps, ...routeToWanda.steps],
                        provider: '智能分段导航',
                        segments: [
                            { name: '校园内路径', route: routeToExit, exit: bestExit },
                            { name: '校外路径', route: routeToWanda }
                        ]
                    };

                    displayRoute(combinedRoute);
                    isNavigating = true;
                    lastRouteUpdate = Date.now();

                    const totalDistance = (combinedRoute.distance / 1000).toFixed(2);
                    const totalTime = Math.round(combinedRoute.duration / 60);

                    log(`✅ 万达广场智能导航启动成功！`, 'success');
                    log(`📏 总距离: ${totalDistance}km, 预计时间: ${totalTime}分钟`, 'success');
                    log(`🚪 路径: 当前位置 → ${bestExit.name} → 万达广场`, 'info');
                    log('💡 系统将根据您的位置变化自动更新路线', 'info');

                    currentDestination = { lat: WANDA_LAT, lon: WANDA_LON, name: '万达广场' };
                } else {
                    throw new Error('路径规划失败');
                }
            } catch (error) {
                log(`❌ 智能导航启动失败: ${error.message}`, 'error');
                log('🔄 尝试直接路径规划...', 'info');

                // 备选方案：直接路径规划
                const directRoute = await planRoute(currentLat, currentLon, WANDA_LAT, WANDA_LON);
                if (directRoute) {
                    displayRoute(directRoute);
                    isNavigating = true;
                    lastRouteUpdate = Date.now();
                    log(`✅ 直接路径导航启动成功！距离: ${(directRoute.distance/1000).toFixed(2)}km`, 'success');
                    currentDestination = { lat: WANDA_LAT, lon: WANDA_LON, name: '万达广场' };
                } else {
                    log('❌ 万达广场导航完全失败', 'error');
                    currentDestination = null;
                }
            }
        }

        async function planCustomRoute() {
            const startCoords = prompt('请输入起点坐标 (格式: 纬度,经度):', `${DEFAULT_LAT},${DEFAULT_LON}`);
            const endCoords = prompt('请输入终点坐标 (格式: 纬度,经度):', '26.8869,112.6758');

            if (startCoords && endCoords) {
                try {
                    const [startLat, startLon] = startCoords.split(',').map(x => parseFloat(x.trim()));
                    const [endLat, endLon] = endCoords.split(',').map(x => parseFloat(x.trim()));

                    if (isNaN(startLat) || isNaN(startLon) || isNaN(endLat) || isNaN(endLon)) {
                        throw new Error('坐标格式错误');
                    }

                    log(`🗺️ 自定义路径规划: (${startLat}, ${startLon}) → (${endLat}, ${endLon})`, 'info');

                    const routeData = await planRoute(startLat, startLon, endLat, endLon);

                    if (routeData) {
                        displayRoute(routeData);
                        isNavigating = true;
                        log(`✅ 自定义路径规划成功！`, 'success');
                    }
                } catch (error) {
                    log(`❌ 自定义路径规划失败: ${error.message}`, 'error');
                    alert('坐标格式错误，请使用格式: 纬度,经度');
                }
            }
        }

        function clearRoute() {
            if (routeLayer) {
                map.removeLayer(routeLayer);
                routeLayer = null;
            }

            // 清除所有标记（除了当前位置标记）
            map.eachLayer(function(layer) {
                if (layer instanceof L.Marker && layer !== currentMarker) {
                    map.removeLayer(layer);
                }
            });

            // 隐藏导航信息
            document.getElementById('navigationInfo').style.display = 'none';
            document.getElementById('routeInstructions').style.display = 'none';

            // 停止导航并清除目的地
            isNavigating = false;
            currentDestination = null;
            lastRouteUpdate = 0;
            currentRouteSteps = [];
            currentStepIndex = 0;
            lastStepCheckTime = 0;

            log('🧹 路径已清除，导航已停止', 'info');
        }

        function toggleInstructions() {
            const instructions = document.getElementById('routeInstructions');
            if (instructions.style.display === 'none' || instructions.style.display === '') {
                instructions.style.display = 'block';
                log('📋 显示导航指令', 'info');
            } else {
                instructions.style.display = 'none';
                log('📋 隐藏导航指令', 'info');
            }
        }



        // 初始化应用
        window.onload = function() {
            log('🚀 STM32F407VET6 GPS追踪导航系统启动中...', 'info');

            initMap();
            loadGPSFromThingSpeak();

            // 每10秒自动刷新GPS数据
            setInterval(loadGPSFromThingSpeak, 10000);

            log('✅ 系统初始化完成！', 'success');
            log('💡 提示: 点击"万达导航"开始路径规划', 'info');
        };
    </script>
</body>
</html>
