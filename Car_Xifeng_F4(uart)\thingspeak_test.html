<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ThingSpeak连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #40a9ff;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
        #log {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ThingSpeak连接测试工具</h1>
        
        <div class="test-section">
            <h3>📋 配置信息</h3>
            <p><strong>频道ID:</strong> <span id="channelId">3014831</span></p>
            <p><strong>读取API密钥:</strong> <span id="readKey">V64RR7CZJ9Z4O7ED</span></p>
            <p><strong>写入API密钥:</strong> <span id="writeKey">LU22ZUP4ZTFK4IY9</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 测试功能</h3>
            <button class="btn" onclick="testReadAPI()">📖 测试读取API</button>
            <button class="btn" onclick="testWriteAPI()">📝 测试写入API</button>
            <button class="btn" onclick="testBothAPIs()">🔄 完整测试</button>
            <button class="btn" onclick="clearLog()">🧹 清除日志</button>
        </div>

        <div class="test-section">
            <h3>📊 测试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        const CHANNEL_ID = '3014831';
        const READ_KEY = 'V64RR7CZJ9Z4O7ED';
        const WRITE_KEY = 'LU22ZUP4ZTFK4IY9';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testReadAPI() {
            log('🔍 开始测试读取API...', 'info');
            
            try {
                // 测试带API密钥的请求
                const urlWithKey = `https://api.thingspeak.com/channels/${CHANNEL_ID}/feeds/last.json?api_key=${read_KEY}`;
                log(`📡 请求URL: ${urlWithKey}`, 'info');
                
                const response = await fetch(urlWithKey);
                log(`📊 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ 读取成功! 数据: ${JSON.stringify(data, null, 2)}`, 'success');
                
                if (data === -1) {
                    log('⚠️ 返回-1，可能是频道私有或无数据', 'error');
                } else if (data.field1 && data.field2) {
                    log(`📍 GPS数据: 纬度=${data.field1}, 经度=${data.field2}, 高度=${data.field3 || 'N/A'}`, 'success');
                    log(`⏰ 数据时间: ${data.created_at}`, 'info');
                } else {
                    log('⚠️ 数据格式异常', 'error');
                }
                
            } catch (error) {
                log(`❌ 读取API测试失败: ${error.message}`, 'error');
                
                // 尝试无API密钥的请求（适用于公开频道）
                try {
                    log('🔄 尝试无API密钥请求...', 'info');
                    const publicUrl = `https://api.thingspeak.com/channels/${CHANNEL_ID}/feeds/last.json`;
                    const publicResponse = await fetch(publicUrl);
                    
                    if (publicResponse.ok) {
                        const publicData = await publicResponse.json();
                        log(`✅ 公开访问成功! 数据: ${JSON.stringify(publicData, null, 2)}`, 'success');
                    } else {
                        log(`❌ 公开访问也失败: ${publicResponse.status}`, 'error');
                    }
                } catch (publicError) {
                    log(`❌ 公开访问异常: ${publicError.message}`, 'error');
                }
            }
        }

        async function testWriteAPI() {
            log('📝 开始测试写入API...', 'info');
            
            try {
                // 生成测试数据
                const testLat = 26.8812 + (Math.random() - 0.5) * 0.001;
                const testLon = 112.6769 + (Math.random() - 0.5) * 0.001;
                const testAlt = 68.0 + Math.random() * 10;
                
                log(`📤 测试数据: 纬度=${testLat.toFixed(6)}, 经度=${testLon.toFixed(6)}, 高度=${testAlt.toFixed(1)}`, 'info');
                
                const writeUrl = `https://api.thingspeak.com/update?api_key=${WRITE_KEY}&field1=${testLat}&field2=${testLon}&field3=${testAlt}`;
                log(`📡 写入URL: ${writeUrl}`, 'info');
                
                const response = await fetch(writeUrl, { method: 'GET' });
                const result = await response.text();
                
                log(`📊 写入响应: ${result}`, 'info');
                
                if (result && result !== '0' && !isNaN(parseInt(result))) {
                    log(`✅ 写入成功! Entry ID: ${result}`, 'success');
                    log('⏳ 等待3秒后验证写入结果...', 'info');
                    
                    // 等待3秒后读取验证
                    setTimeout(async () => {
                        try {
                            const verifyResponse = await fetch(`https://api.thingspeak.com/channels/${CHANNEL_ID}/feeds/last.json?api_key=${read_KEY}`);
                            const verifyData = await verifyResponse.json();
                            
                            if (verifyData && verifyData.field1) {
                                const readLat = parseFloat(verifyData.field1);
                                const readLon = parseFloat(verifyData.field2);
                                
                                if (Math.abs(readLat - testLat) < 0.000001 && Math.abs(readLon - testLon) < 0.000001) {
                                    log('✅ 写入验证成功! 数据一致', 'success');
                                } else {
                                    log(`⚠️ 数据不一致: 写入(${testLat.toFixed(6)}, ${testLon.toFixed(6)}) vs 读取(${readLat.toFixed(6)}, ${readLon.toFixed(6)})`, 'error');
                                }
                            } else {
                                log('❌ 验证失败: 无法读取写入的数据', 'error');
                            }
                        } catch (verifyError) {
                            log(`❌ 验证异常: ${verifyError.message}`, 'error');
                        }
                    }, 3000);
                    
                } else {
                    log(`❌ 写入失败: ${result}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 写入API测试失败: ${error.message}`, 'error');
            }
        }

        async function testBothAPIs() {
            log('🔄 开始完整测试...', 'info');
            log('=' .repeat(50), 'info');
            
            await testReadAPI();
            
            log('=' .repeat(50), 'info');
            
            await testWriteAPI();
            
            log('=' .repeat(50), 'info');
            log('✅ 完整测试结束', 'success');
        }

        // 页面加载时显示配置信息
        window.onload = function() {
            log('🚀 ThingSpeak测试工具已加载', 'success');
            log(`📋 频道ID: ${CHANNEL_ID}`, 'info');
            log(`🔑 读取密钥: ${read_KEY}`, 'info');
            log(`🔑 写入密钥: ${WRITE_KEY}`, 'info');
            log('点击测试按钮开始测试...', 'info');
        };
    </script>
</body>
</html>
